"use server";

import { db } from "@/db";
import { friend, user } from "@/db/schema";
import { and, eq, or } from "drizzle-orm";

export const sendRequest = async (sender: string, receiver: string): Promise<void> => {
  await db.insert(friend).values({ sender, receiver, status: false });
};

export const acceptRequest = async (sender: string, receiver: string): Promise<void> => {
  await db
    .update(friend)
    .set({ status: true })
    .where(and(eq(friend.sender, sender), eq(friend.receiver, receiver)));
};

export const denyRequest = async (sender: string, receiver: string): Promise<void> => {
  await db.delete(friend).where(and(eq(friend.sender, sender), eq(friend.receiver, receiver)));
};

export const getFromMeRequests = async (
  userId: string
): Promise<
  {
    id: string;
    name: string;
    image: string | null;
    bio: string | null;
    sender: string;
    receiver: string;
  }[]
> => {
  return db
    .select({
      id: user.id,
      name: user.name,
      image: user.image,
      bio: user.bio,
      sender: friend.sender,
      receiver: friend.receiver,
    })
    .from(friend)
    .innerJoin(user, eq(friend.receiver, user.id))
    .where(and(eq(friend.status, false), eq(friend.sender, userId)));
};

export const getForMeRequests = async (
  userId: string
): Promise<
  {
    id: string;
    name: string;
    image: string | null;
    bio: string | null;
    sender: string;
    receiver: string;
  }[]
> => {
  return db
    .select({
      id: user.id,
      name: user.name,
      image: user.image,
      bio: user.bio,
      sender: friend.sender,
      receiver: friend.receiver,
    })
    .from(friend)
    .innerJoin(user, eq(friend.sender, user.id))
    .where(and(eq(friend.status, false), eq(friend.receiver, userId)));
};

export const getFriends = async (
  userId: string
): Promise<
  {
    id: string;
    name: string;
    image: string | null;
    bio: string | null;
    sender: string;
    receiver: string;
  }[]
> => {
  return db
    .select({
      id: user.id,
      name: user.name,
      image: user.image,
      bio: user.bio,
      sender: friend.sender,
      receiver: friend.receiver,
    })
    .from(friend)
    .innerJoin(user, or(eq(friend.sender, user.id), eq(friend.receiver, user.id)))
    .where(and(eq(friend.status, true), or(eq(friend.sender, userId), eq(friend.receiver, userId))))
    .then((friends) => friends.filter((f) => f.id !== userId));
};
