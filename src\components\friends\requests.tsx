"use client";

import { denyRequest, getForMeRequests, getFromMeRequests } from "@/server/friend";
import { User } from "better-auth";
import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Check, X } from "lucide-react";
import { toast } from "../ui/toast";

export default ({ user, forMe = true }: { user: User; forMe?: boolean }) => {
  const [requests, setRequests] = React.useState<Partial<User>[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);
  React.useEffect(() => {
    (async () => {
      setRequests(await (forMe ? getForMeRequests(user.id) : getFromMeRequests(user.id)));
      setLoading(false);
    })();
  }, [user.id]);
  if (loading) return <span>Loading...</span>;
  return (
    <ul className="flex flex-col gap-2">
      {requests.map((f) => (
        <li key={f.id} className="flex items-center justify-between gap-2">
          {f.image && (
            <Avatar>
              <AvatarImage src={f.image} alt="profile-picture" />
              <AvatarFallback>CN</AvatarFallback>
            </Avatar>
          )}
          <span className="truncate">{f.name}</span>
          <div className="flex flex-row gap-1">
            <Button size={"icon"}>
              <Check />
            </Button>
            <Button
              variant={"destructive"}
              size={"icon"}
              onClick={async () => {
                try {
                  await denyRequest(f.id as string, user.id);
                  requests.filter((e) => e.id == f.id);
                  toast.success("Friend request denied");
                } catch {
                  toast.error("Error during denying friend request");
                }
              }}>
              <X />
            </Button>
          </div>
        </li>
      ))}
    </ul>
  );
};
