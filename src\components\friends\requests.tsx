"use client";

import { denyRequest, getForMeRequests, getFromMeRequests, acceptRequest } from "@/server/friend";
import { User } from "better-auth";
import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Check, X } from "lucide-react";
import { toast } from "../ui/toast";

export default ({ user, forMe = true }: { user: User; forMe?: boolean }) => {
  const [requests, setRequests] = React.useState<Partial<User>[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);

  const loadRequests = async () => {
    setLoading(true);
    setRequests(await (forMe ? getForMeRequests(user.id) : getFromMeRequests(user.id)));
    setLoading(false);
  };

  React.useEffect(() => {
    loadRequests();
  }, [user.id, forMe]);
  if (loading) return <div className="text-center text-muted-foreground">Loading...</div>;

  if (requests.length === 0) {
    return <div className="text-center text-muted-foreground">{forMe ? "No pending requests" : "No sent requests"}</div>;
  }

  return (
    <ul className="flex flex-col gap-2">
      {requests.map((f) => (
        <li key={f.id} className="flex items-center justify-between gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors">
          <div className="flex items-center gap-2">
            <Avatar>
              <AvatarImage src={f.image || ""} alt={`${f.name}'s profile picture`} />
              <AvatarFallback>{f.name?.charAt(0)?.toUpperCase() || "?"}</AvatarFallback>
            </Avatar>
            <span className="truncate font-medium">{f.name}</span>
          </div>
          <div className="flex flex-row gap-1">
            {forMe && (
              <Button
                size={"icon"}
                onClick={async () => {
                  try {
                    await acceptRequest(f.id as string, user.id);
                    await loadRequests(); // Reload the requests
                    toast.success("Friend request accepted");
                  } catch {
                    toast.error("Error during accepting friend request");
                  }
                }}>
                <Check />
              </Button>
            )}
            <Button
              variant={"destructive"}
              size={"icon"}
              onClick={async () => {
                try {
                  if (forMe) {
                    // For requests to me, deny using sender and receiver
                    await denyRequest(f.id as string, user.id);
                  } else {
                    // For requests from me, deny using user and receiver
                    await denyRequest(user.id, f.id as string);
                  }
                  await loadRequests(); // Reload the requests
                  toast.success("Friend request denied");
                } catch {
                  toast.error("Error during denying friend request");
                }
              }}>
              <X />
            </Button>
          </div>
        </li>
      ))}
    </ul>
  );
};
