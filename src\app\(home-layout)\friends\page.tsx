import AddFriend from "@/components/friends/addFriend";
import Friends from "@/components/friends/friends";
import GetId from "@/components/friends/getId";
import Requests from "@/components/friends/requests";
import { Separator } from "@/components/ui/separator";
import { auth } from "@/lib/auth";
import { User } from "better-auth";
import { headers } from "next/headers";

export default async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) return null;

  return (
    <div className="grid grid-cols-1 md:grid-cols-[1fr_0.5rem_1fr_0.5rem_1fr] h-[calc(100dvh-2rem)]">
      <section>
        <h1 className="text-center">Utility</h1>
        <Separator className="my-2 py-0.5 hidden md:block" />
        <div className="pr-3 pt-2 flex flex-col gap-4">
          <GetId user={session?.user as User} />
          <AddFriend user={session?.user as User} />
        </div>
      </section>
      <Separator orientation="vertical" className="my-1 px-0.5 hidden md:block" />
      <section>
        <h1 className="text-center">Friends</h1>
        <Separator className="my-2 py-0.5 hidden md:block" />
        <Friends user={session?.user as User} />
      </section>
      <Separator orientation="vertical" className="my-1 px-0.5 hidden md:block" />

      <section>
        <div className="h-1/2">
          <h1 className="text-center">My requests</h1>
          <Separator className="my-2 py-0.5 hidden md:block" />
          <Requests user={session?.user as User} forMe={false} />
        </div>
        <div className="h-1/2">
          <h1 className="text-center">Requests for me</h1>
          <Separator className="my-2 py-0.5 hidden md:block" />
          <Requests user={session?.user as User} forMe={true} />
        </div>
      </section>
    </div>
  );
};
