import FriendsClient from "@/components/friends/friends-client";
import { auth } from "@/lib/auth"; // your better-auth instance
import { getFriends, sendFriendRequestByEmail, acceptFriendRequest, removeFriend } from "@/server/friends";
import { headers } from "next/headers";

export default async function FriendsPage() {
  const session = await auth.api.getSession({
    headers: await headers(), // you need to pass the headers object.
  });

  if (!session?.user) return null;
  
  const userId = session.user.id;
  const friends = await getFriends(userId);

  // Server actions
  async function sendAction(data: { senderId: string; email: string }) {
    'use server'
    await sendFriendRequestByEmail(data.senderId, data.email);
  }

  async function acceptAction(data: { senderId: string; receiverId: string }) {
    'use server'
    await acceptFriendRequest(data.senderId, data.receiverId);
  }

  async function removeAction(data: { senderId: string; receiverId: string }) {
    'use server'
    await removeFriend(data.senderId, data.receiverId);
  }

  return <FriendsClient friends={friends} userId={userId} sendAction={sendAction} acceptAction={acceptAction} removeAction={removeAction} />;
}
