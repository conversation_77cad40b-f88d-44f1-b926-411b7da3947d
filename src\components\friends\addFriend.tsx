"use client";

import { User } from "better-auth";
import { <PERSON><PERSON> } from "../ui/button";
import { toast } from "../ui/toast";
import * as D from "@/components/ui/dialog";
import { Input } from "../ui/input";
import React from "react";
import { sendRequest } from "@/server/friend";

export default ({ user }: { user: User }) => {
  const [friend, setFriend] = React.useState<string>("");
  return (
    <D.Dialog>
      <D.DialogTrigger asChild>
        <Button>Add friend</Button>
      </D.DialogTrigger>
      <D.DialogContent>
        <D.DialogHeader>
          <D.DialogTitle>Add new friend</D.DialogTitle>
          <D.DialogDescription>Get friends ID in order to add them to your friends</D.DialogDescription>
        </D.DialogHeader>
        <Input type="text" value={friend} onChange={(e) => setFriend(e.target.value)} />
        <D.DialogFooter>
          <Button
            disabled={friend.length === 0}
            onClick={async () => {
              try {
                await sendRequest(user.id, friend);
                toast.success("New friend added");
              } catch (e: any) {
                toast.error(e.message);
              }
            }}>
            Add friend
          </Button>
        </D.DialogFooter>
      </D.DialogContent>
    </D.Dialog>
  );
};
