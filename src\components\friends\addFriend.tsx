"use client";

import { User } from "better-auth";
import { <PERSON><PERSON> } from "../ui/button";
import { toast } from "../ui/toast";
import * as D from "@/components/ui/dialog";
import { Input } from "../ui/input";
import React from "react";
import { sendRequest } from "@/server/friend";

export default ({ user }: { user: User }) => {
  const [friend, setFriend] = React.useState<string>("");
  const [open, setOpen] = React.useState<boolean>(false);
  const [loading, setLoading] = React.useState<boolean>(false);

  const handleAddFriend = async () => {
    if (friend.length === 0) return;

    setLoading(true);
    try {
      await sendRequest(user.id, friend);
      toast.success("Friend request sent!");
      setFriend("");
      setOpen(false);
    } catch (e: any) {
      toast.error(e.message || "Failed to send friend request");
    }
    setLoading(false);
  };

  return (
    <D.Dialog open={open} onOpenChange={setOpen}>
      <D.DialogTrigger asChild>
        <Button>Add friend</Button>
      </D.DialogTrigger>
      <D.DialogContent>
        <D.DialogHeader>
          <D.DialogTitle>Add new friend</D.DialogTitle>
          <D.DialogDescription>Enter your friend's ID to send them a friend request</D.DialogDescription>
        </D.DialogHeader>
        <Input
          type="text"
          placeholder="Friend's ID"
          value={friend}
          onChange={(e) => setFriend(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleAddFriend();
            }
          }}
        />
        <D.DialogFooter>
          <Button disabled={friend.length === 0 || loading} onClick={handleAddFriend}>
            {loading ? "Sending..." : "Send Request"}
          </Button>
        </D.DialogFooter>
      </D.DialogContent>
    </D.Dialog>
  );
};
