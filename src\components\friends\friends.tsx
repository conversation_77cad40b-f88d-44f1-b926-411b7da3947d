"use client";

import { getFriends } from "@/server/friend";
import { User } from "better-auth";
import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

export default ({ user }: { user: User }) => {
  const [friends, setFriends] = React.useState<Partial<User>[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);
  React.useEffect(() => {
    (async () => {
      setFriends(await getFriends(user.id));
      setLoading(false);
    })();
  }, [user.id]);
  if (loading) return <span>Loading...</span>;
  return (
    <ul className="flex flex-col gap-2">
      {friends.map((f) => (
        <li key={f.id} className="flex items-center gap-2">
          {f.image && (
            <Avatar>
              <AvatarImage src={f.image} alt="profile-picture" />
              <AvatarFallback>CN</AvatarFallback>
            </Avatar>
          )}
          <span className="truncate">{f.name}</span>
        </li>
      ))}
    </ul>
  );
};
