"use client";

import { getFriends } from "@/server/friend";
import { User } from "better-auth";
import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

export default ({ user }: { user: User }) => {
  const [friends, setFriends] = React.useState<Partial<User>[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);

  React.useEffect(() => {
    (async () => {
      setLoading(true);
      setFriends(await getFriends(user.id));
      setLoading(false);
    })();
  }, [user.id]);

  if (loading) return <div className="text-center text-muted-foreground">Loading friends...</div>;

  if (friends.length === 0) {
    return <div className="text-center text-muted-foreground">No friends yet. Add some friends to get started!</div>;
  }

  return (
    <ul className="flex flex-col gap-2">
      {friends.map((f) => (
        <li key={f.id} className="flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors">
          <Avatar>
            <AvatarImage src={f.image || ""} alt={`${f.name}'s profile picture`} />
            <AvatarFallback>{f.name?.charAt(0)?.toUpperCase() || "?"}</AvatarFallback>
          </Avatar>
          <span className="truncate font-medium">{f.name}</span>
        </li>
      ))}
    </ul>
  );
};
